# PPSVMT - 隐私保护支持向量机训练

## 项目概述

PPSVMT（隐私保护支持向量机训练）是一个用于医疗诊断的隐私保护机器学习系统的研究实现。该项目实现了一种联邦学习方法，用于训练支持向量机（SVM）模型，同时使用同态加密保护敏感的医疗数据。

该系统通过使多个数据提供者能够在不暴露原始数据的情况下协作训练SVM模型，解决了医疗保健中的"信息孤岛"问题。它使用Paillier密码系统对模型参数进行加密，允许数据提供者计算局部梯度，同时模型训练者安全地聚合梯度并更新参数。

## 项目结构

```
PPSVMT-main/
├── GD_SVM.py                    # 主要的加密SVM实现
├── modelTrain.py                # 核心训练算法（加密版）
├── modelTest.py                 # 模型评估函数
├── experiment_method.py         # 实验评估方法
├── data_divide.py               # 数据预处理和交叉验证
├── requirements.txt             # Python依赖项
├── README.md                    # 基本项目描述（中文）
├── dataSet/                     # 医疗数据集
│   ├── 01breast-cancer-wisconsin.txt
│   ├── 02wdbc.txt
│   ├── 03wpbc.txt
│   ├── 04Pima Indian Diabetic.txt
│   └── 05Heart Disease Data Set.txt
└── Unencrypt/                   # 非加密基线实现
    ├── GD_SVM.py
    ├── modelTrain.py
    ├── modelTest.py
    ├── experiment_method.py
    ├── data_divide.py
    └── result/                  # 结果输出目录
```

## 主要特性

### 隐私保护训练
- **同态加密**：使用Paillier密码系统进行安全计算
- **联邦学习**：在不共享数据的情况下实现协作训练
- **安全比较**：为加密值实现安全比较协议
- **梯度隐私**：保护数据样本和模型参数

### 多种训练算法
- **exp_model**：使用指数泰勒展开作为损失函数（主要的PPSVMT算法）
- **log_model**：使用对数损失函数
- **old_model**：传统的安全SVM实现

### 数据处理
- **多种标准化方法**：4种不同的标准化方法
- **交叉验证**：10折交叉验证进行稳健评估
- **医疗数据集支持**：针对医疗诊断数据集进行优化

### 性能监控
- **时间分析**：分别跟踪DP（数据提供者）和DAC（数据聚合中心）的计算时间
- **混淆矩阵**：分类性能的可视化评估
- **综合指标**：精确率、召回率、准确率、TP/FP/TN/FN统计

## 技术栈

### 编程语言
- **Python 3.x**：核心实现语言

### 主要库
- **numpy (≥1.9.1)**：数值计算和矩阵操作
- **phe (≥1.4.0)**：Paillier同态加密库
- **gmpy2 (≥2.0.4)**：用于密码学操作的高性能算术库
- **matplotlib**：可视化和绘图
- **seaborn**：统计数据可视化
- **sklearn**：机器学习工具（指标）

### 密码学框架
- **Paillier密码系统**：提供加法同态特性
- **1024位密钥长度**：加密的默认安全参数

## 安装和设置

### 前置条件
- Python 3.x
- pip包管理器

### 安装步骤

1. **克隆仓库**：
   ```bash
   git clone <repository-url>
   cd PPSVMT-main
   ```

2. **安装依赖项**：
   ```bash
   pip install -r requirements.txt
   ```

3. **验证安装**：
   ```bash
   python -c "import phe, numpy, gmpy2; print('所有依赖项安装成功')"
   ```

## 使用说明

### 运行主要实验

1. **执行加密版本**：
   ```bash
   python GD_SVM.py
   ```

2. **执行基线（非加密）版本**：
   ```bash
   python Unencrypt/GD_SVM.py
   ```

### 配置选项

主脚本允许配置多个参数：

```python
# 数据标准化方法
stand_method = standard4  # 选项：normalization1, linear2, range3, standard4

# 评估方法
evaluation_method = cross_train  # 选项：holdout_train, cross_train, one_train

# 训练算法
train_method = exp_model  # 选项：old_model, exp_model, log_model

# 加密参数
n_l = 1024  # Paillier加密的密钥长度
```

### 数据集格式

数据集应为逗号分隔的文本文件，包含：
- 第1到n-1列为特征
- 最后一列为二元标签（正类为1，负类为-1）
- 缺失值标记为'?'（自动排除）

## 配置

### 关键参数

- **学习率 (lam)**：0.01（默认值）
- **正则化参数 (C)**：1/m（其中m为训练集大小）
- **最大迭代次数**：100-500（因算法而异）
- **收敛精度**：1e-3
- **交叉验证折数**：10

### 安全参数

- **Paillier密钥长度**：1024位
- **安全比较**：使用预定义值的随机掩码

## 依赖项

### 核心依赖项
- **phe**：用于同态加密的Python Paillier库
- **numpy**：科学计算的基础包
- **gmpy2**：多精度算术库

### 可视化依赖项
- **matplotlib**：2D绘图库
- **seaborn**：统计数据可视化
- **sklearn.metrics**：机器学习评估指标

## 架构

### 系统设计

系统采用联邦学习架构，包含两个主要组件：

1. **数据提供者 (DP)**：
   - 持有私有训练数据
   - 计算加密的局部梯度
   - 执行安全比较

2. **数据聚合中心 (DAC)**：
   - 维护加密的模型参数
   - 聚合加密梯度
   - 更新模型参数
   - 解密最终结果

### 隐私模型

- **半诚实对手模型**：各方遵循协议但可能试图推断信息
- **无数据泄露**：原始训练数据永不离开数据提供者
- **参数隐私**：模型参数在训练期间保持加密
- **安全聚合**：在不泄露个体贡献的情况下聚合梯度

### 算法创新

该项目实现了一种使用指数泰勒展开作为损失函数的新方法，具有以下特点：
- 与传统SVM训练相比降低了计算复杂度
- 支持同态计算（仅加法和标量乘法）
- 在医疗诊断任务中保持高精度

## 贡献指南

### 代码结构
- 遵循现有的模块化结构
- 分离加密和非加密实现
- 包含性能分析的时间测量
- 记录所有密码学操作

### 测试
- 使用所有提供的医疗数据集进行测试
- 验证隐私保护特性
- 与基线实现比较性能
- 验证收敛特性

### 研究扩展
- 实现额外的损失函数
- 探索不同的密码学方案
- 添加多类分类支持
- 优化通信复杂度

---

*本项目实现了论文"Privacy-Preserving Online Medical Prediagnosis Training Model Based on Soft-Margin SVM"中描述的研究*
