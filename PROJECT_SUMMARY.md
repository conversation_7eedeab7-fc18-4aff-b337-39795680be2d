# PPSVMT - Privacy-Preserving Support Vector Machine Training

## Project Overview

PPSVMT (Privacy-Preserving Support Vector Machine Training) is a research implementation of a privacy-preserving machine learning system for medical diagnosis. The project implements a federated learning approach for training Support Vector Machine (SVM) models while protecting sensitive medical data using homomorphic encryption.

The system addresses the "information silos" problem in healthcare by enabling multiple data providers to collaboratively train SVM models without exposing their raw data. It uses the Paillier cryptosystem to encrypt model parameters, allowing data providers to compute local gradients while the model trainer aggregates gradients and updates parameters securely.

## Project Structure

```
PPSVMT-main/
├── GD_SVM.py                    # Main encrypted SVM implementation
├── modelTrain.py                # Core training algorithms (encrypted)
├── modelTest.py                 # Model evaluation functions
├── experiment_method.py         # Experimental evaluation methods
├── data_divide.py               # Data preprocessing and cross-validation
├── requirements.txt             # Python dependencies
├── README.md                    # Basic project description (Chinese)
├── dataSet/                     # Medical datasets
│   ├── 01breast-cancer-wisconsin.txt
│   ├── 02wdbc.txt
│   ├── 03wpbc.txt
│   ├── 04Pima Indian Diabetic.txt
│   └── 05Heart Disease Data Set.txt
└── Unencrypt/                   # Non-encrypted baseline implementations
    ├── GD_SVM.py
    ├── modelTrain.py
    ├── modelTest.py
    ├── experiment_method.py
    ├── data_divide.py
    └── result/                  # Output directory for results
```

## Key Features

### Privacy-Preserving Training
- **Homomorphic Encryption**: Uses Paillier cryptosystem for secure computation
- **Federated Learning**: Enables collaborative training without data sharing
- **Secure Comparison**: Implements secure comparison protocols for encrypted values
- **Gradient Privacy**: Protects both data samples and model parameters

### Multiple Training Algorithms
- **exp_model**: Uses exponential Taylor expansion as loss function (main PPSVMT algorithm)
- **log_model**: Uses logarithmic loss function
- **old_model**: Traditional secure SVM implementation

### Data Processing
- **Multiple Normalization Methods**: 4 different standardization approaches
- **Cross-Validation**: 10-fold cross-validation for robust evaluation
- **Medical Dataset Support**: Optimized for medical diagnosis datasets

### Performance Monitoring
- **Timing Analysis**: Separate tracking of DP (Data Provider) and DAC (Data Aggregation Center) computation times
- **Confusion Matrix**: Visual evaluation of classification performance
- **Comprehensive Metrics**: Precision, Recall, Accuracy, TP/FP/TN/FN statistics

## Technology Stack

### Programming Language
- **Python 3.x**: Core implementation language

### Key Libraries
- **numpy (≥1.9.1)**: Numerical computations and matrix operations
- **phe (≥1.4.0)**: Paillier homomorphic encryption library
- **gmpy2 (≥2.0.4)**: High-performance arithmetic for cryptographic operations
- **matplotlib**: Visualization and plotting
- **seaborn**: Statistical data visualization
- **sklearn**: Machine learning utilities (metrics)

### Cryptographic Framework
- **Paillier Cryptosystem**: Provides additive homomorphic properties
- **1024-bit Key Length**: Default security parameter for encryption

## Setup and Installation

### Prerequisites
- Python 3.x
- pip package manager

### Installation Steps

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd PPSVMT-main
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify installation**:
   ```bash
   python -c "import phe, numpy, gmpy2; print('All dependencies installed successfully')"
   ```

## Usage Instructions

### Running the Main Experiment

1. **Execute the encrypted version**:
   ```bash
   python GD_SVM.py
   ```

2. **Execute the baseline (unencrypted) version**:
   ```bash
   python Unencrypt/GD_SVM.py
   ```

### Configuration Options

The main script allows configuration of several parameters:

```python
# Data standardization methods
stand_method = standard4  # Options: normalization1, linear2, range3, standard4

# Evaluation methods
evaluation_method = cross_train  # Options: holdout_train, cross_train, one_train

# Training algorithms
train_method = exp_model  # Options: old_model, exp_model, log_model

# Encryption parameters
n_l = 1024  # Key length for Paillier encryption
```

### Dataset Format

Datasets should be comma-separated text files with:
- Features in columns 1 to n-1
- Binary labels in the last column (1 for positive class, -1 for negative class)
- Missing values marked as '?' (automatically excluded)

## Configuration

### Key Parameters

- **Learning Rate (lam)**: 0.01 (default)
- **Regularization Parameter (C)**: 1/m (where m is training set size)
- **Maximum Iterations**: 100-500 (varies by algorithm)
- **Convergence Precision**: 1e-3
- **Cross-Validation Folds**: 10

### Security Parameters

- **Paillier Key Length**: 1024 bits
- **Secure Comparison**: Uses random masking with predefined values

## Dependencies

### Core Dependencies
- **phe**: Python Paillier library for homomorphic encryption
- **numpy**: Fundamental package for scientific computing
- **gmpy2**: Multiple-precision arithmetic library

### Visualization Dependencies
- **matplotlib**: 2D plotting library
- **seaborn**: Statistical data visualization
- **sklearn.metrics**: Machine learning evaluation metrics

## Architecture

### System Design

The system follows a federated learning architecture with two main components:

1. **Data Provider (DP)**: 
   - Holds private training data
   - Computes encrypted local gradients
   - Performs secure comparisons

2. **Data Aggregation Center (DAC)**:
   - Maintains encrypted model parameters
   - Aggregates encrypted gradients
   - Updates model parameters
   - Decrypts final results

### Privacy Model

- **Semi-honest adversary model**: Parties follow the protocol but may try to infer information
- **No data leakage**: Raw training data never leaves data providers
- **Parameter privacy**: Model parameters remain encrypted during training
- **Secure aggregation**: Gradients are aggregated without revealing individual contributions

### Algorithmic Innovation

The project implements a novel approach using exponential Taylor expansion as the loss function, which:
- Reduces computational complexity compared to traditional SVM training
- Enables homomorphic computation (only addition and scalar multiplication)
- Maintains high accuracy for medical diagnosis tasks

## Contributing Guidelines

### Code Structure
- Follow the existing modular structure
- Separate encrypted and unencrypted implementations
- Include timing measurements for performance analysis
- Document all cryptographic operations

### Testing
- Test with all provided medical datasets
- Verify privacy preservation properties
- Compare performance with baseline implementations
- Validate convergence properties

### Research Extensions
- Implement additional loss functions
- Explore different cryptographic schemes
- Add support for multi-class classification
- Optimize communication complexity

---

*This project implements the research described in "Privacy-Preserving Online Medical Prediagnosis Training Model Based on Soft-Margin SVM"*
